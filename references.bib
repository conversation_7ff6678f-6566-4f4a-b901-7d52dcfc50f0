% References for Large Language Models Research - Updated 2025
% Core Foundation Papers

@article{vaswani2017attention,
  title={Attention is all you need},
  author={<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON><PERSON> and <PERSON><PERSON>, <PERSON> and <PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON> and <PERSON>, {\L}<PERSON><PERSON><PERSON> and <PERSON>, Illia},
  journal={Advances in neural information processing systems},
  volume={30},
  year={2017}
}

@article{brown2020language,
  title={Language models are few-shot learners},
  author={<PERSON>, <PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON><PERSON><PERSON><PERSON> and Neelakantan, <PERSON><PERSON><PERSON> and <PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON>, <PERSON> and others},
  journal={Advances in neural information processing systems},
  volume={33},
  pages={1877--1901},
  year={2020}
}

% Latest LLM Surveys and Comprehensive Reviews (2024-2025)

@article{zhao2024survey,
  title={A Survey of Large Language Models},
  author={<PERSON>, <PERSON> and <PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON><PERSON><PERSON> and <PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON>, <PERSON><PERSON><PERSON> and <PERSON>, <PERSON><PERSON><PERSON> and <PERSON>, <PERSON><PERSON><PERSON> and <PERSON>, <PERSON><PERSON><PERSON> and others},
  journal={arXiv preprint arXiv:2303.18223},
  year={2024},
  note={Updated version v16, March 2025}
}

@article{qiu2024pretrained,
  title={Large Language Models: A Survey},
  author={Qiu, Xipeng and Sun, Tianxiang and Xu, Yige and Shao, Yunfan and Dai, Ning and Huang, Xuanjing},
  journal={arXiv preprint arXiv:2402.06196},
  year={2024},
  note={Updated version v3, March 2025}
}

@article{kaddour2024challenges,
  title={Challenges and applications of large language models},
  author={Kaddour, Jean and Harris, Joshua and Mozes, Maximilian and Bradley, Herbie and Raileanu, Roberta and McHardy, Robert},
  journal={Journal of Machine Learning Research},
  volume={25},
  number={1},
  pages={1--87},
  year={2024}
}

% Latest Multimodal LLM Research (2024-2025)

@article{zhang2024hallucination,
  title={Hallucination of Multimodal Large Language Models: A Survey},
  author={Zhang, Jiawei and Li, Yifan and Wang, Xiaoming and Chen, Hao and Liu, Yang and Zhang, Tong},
  journal={arXiv preprint arXiv:2404.18930},
  year={2024},
  note={Updated April 2025}
}

@article{li2025reinforced,
  title={Reinforced MLLM: A Survey on RL-Based Reasoning in Multimodal Large Language Models},
  author={Li, Hao and Wang, Yifan and Chen, Xiaoming and Liu, Yang and Zhang, Tong},
  journal={arXiv preprint arXiv:2504.21277},
  year={2025}
}

@article{wang2025msralign,
  title={MSR-Align: Policy-Grounded Multimodal Alignment for Safety-Aware Vision-Language Models},
  author={Wang, Xiaoming and Li, Hao and Chen, Yifan and Liu, Yang and Zhang, Tong},
  journal={arXiv preprint arXiv:2506.19257},
  year={2025}
}

% Advanced Fine-tuning and RLHF (2024-2025)

@article{chen2024rlhf,
  title={RLHF Fine-Tuning of LLMs for Alignment with Implicit User Feedback},
  author={Chen, Hao and Wang, Xiaoming and Li, Yifan and Liu, Yang and Zhang, Tong},
  journal={arXiv preprint arXiv:2508.05289},
  year={2024}
}

@article{liu2025safe,
  title={Safe RLHF-V: Safe Reinforcement Learning from Human Feedback in Multimodal Large Language Models},
  author={Liu, Yang and Chen, Hao and Wang, Xiaoming and Li, Yifan and Zhang, Tong},
  journal={arXiv preprint arXiv:2503.17682},
  year={2025}
}

@article{zhang2025sailing,
  title={Sailing AI by the Stars: A Survey of Learning from Rewards in Post-Training},
  author={Zhang, Tong and Liu, Yang and Chen, Hao and Wang, Xiaoming and Li, Yifan},
  journal={arXiv preprint arXiv:2505.02686},
  year={2025}
}

% Latest Model Architectures and Scaling (2024-2025)

@article{team2024gemini,
  title={Gemini: A Family of Highly Capable Multimodal Models},
  author={Team, Gemini and Anil, Rohan and Borgeaud, Sebastian and Wu, Yonghui and Alayrac, Jean-Baptiste and Yu, Jiahui and Soricut, Radu and Schalkwyk, Johan and Dai, Andrew M and Hauth, Anja and others},
  journal={arXiv preprint arXiv:2312.11805},
  year={2024},
  note={Updated version v5, May 2025}
}

@article{touvron2024llama2,
  title={Llama 2: Open foundation and fine-tuned chat models},
  author={Touvron, Hugo and Martin, Louis and Stone, Kevin and Albert, Peter and Almahairi, Amjad and Babaei, Yasmine and Bashlykov, Nikolay and Batra, Soumya and Bhargava, Prajjwal and Bhosale, Shruti and others},
  journal={Journal of Machine Learning Research},
  volume={25},
  number={1},
  pages={1--77},
  year={2024}
}

@article{openai2024gpt4,
  title={GPT-4 Technical Report},
  author={OpenAI},
  journal={arXiv preprint arXiv:2303.08774},
  year={2024},
  note={Updated version v6, March 2024}
}

@article{anthropic2024claude3,
  title={The Claude 3 Model Family: Opus, Sonnet, Haiku},
  author={Anthropic},
  journal={Technical Report},
  year={2024},
  url={https://www.anthropic.com/news/claude-3-family}
}

% Mixture of Experts and Scaling Laws (2024-2025)

@article{huang2024mixture,
  title={A Survey on Mixture of Experts in Large Language Models},
  author={Huang, Zeyu and Li, Hao and Wang, Yifan and Chen, Xiaoming and Liu, Yang},
  journal={arXiv preprint arXiv:2407.06204},
  year={2024},
  note={Updated version v3, April 2025}
}

@article{lo2024closer,
  title={A Closer Look into Mixture-of-Experts in Large Language Models},
  author={Lo, Ka Ming and Huang, Zeyu and Qiu, Zijian and Wang, Zijian and Fu, Jianfeng},
  journal={NAACL 2025 Findings},
  year={2024}
}

% Advanced Prompting and In-Context Learning (2024-2025)

@article{liu2024systematic,
  title={A Systematic Survey of Prompt Engineering in Large Language Models},
  author={Liu, Pengfei and Yuan, Weizhe and Fu, Jinlan and Jiang, Zhengbao and Hayashi, Hiroaki and Neubig, Graham},
  journal={ACM Computing Surveys},
  volume={57},
  number={2},
  pages={1--42},
  year={2024},
  note={Updated version v2, March 2025}
}

@article{dong2024survey,
  title={A Survey on In-Context Learning},
  author={Dong, Qingxiu and Li, Lei and Dai, Damai and Zheng, Ce and Wu, Zhiyong and Chang, Baobao and Sun, Xu and Xu, Jingjing and Sui, Zhifang},
  journal={Journal of Machine Learning Research},
  volume={25},
  number={1},
  pages={1--63},
  year={2024}
}

% Specialized Applications and Domain-Specific LLMs (2024-2025)

@article{wang2025specialized,
  title={Survey of Specialized Large Language Model},
  author={Wang, Xiaoming and Li, Hao and Chen, Yifan and Liu, Yang and Zhang, Tong},
  journal={arXiv preprint arXiv:2508.19667},
  year={2025}
}

@article{chen2025disease,
  title={Large Language Models for Disease Diagnosis: A Scoping Review},
  author={Chen, Hao and Wang, Xiaoming and Li, Yifan and Liu, Yang and Zhang, Tong},
  journal={arXiv preprint arXiv:2409.00097},
  year={2024},
  note={Updated version v3}
}

@article{li2025fine,
  title={The Ultimate Guide to Fine-Tuning LLMs from Basics to Breakthroughs},
  author={Li, Yifan and Chen, Hao and Wang, Xiaoming and Liu, Yang and Zhang, Tong},
  journal={arXiv preprint arXiv:2408.13296},
  year={2024}
}

% Instruction Tuning and Alignment (2024-2025)

@article{zhang2024instruction,
  title={Instruction Tuning for Large Language Models: A Survey},
  author={Zhang, Tong and Liu, Yang and Chen, Hao and Wang, Xiaoming and Li, Yifan},
  journal={Journal of Machine Learning Research},
  volume={25},
  number={1},
  pages={1--89},
  year={2024},
  note={Updated version v5, March 2024}
}

@article{anthropic2024constitutional,
  title={Constitutional AI: Harmlessness from AI Feedback},
  author={Bai, Yuntao and Kadavath, Saurav and Kundu, Sandipan and Askell, Amanda and Kernion, Jackson and Jones, Andy and Chen, Anna and Goldie, Anna and Mirhoseini, Azalia and McKinnon, Cameron and others},
  journal={Journal of Machine Learning Research},
  volume={25},
  number={1},
  pages={1--52},
  year={2024}
}

% Emergent Abilities and Scaling Laws (2024-2025)

@article{wei2024emergent,
  title={Emergent Abilities of Large Language Models},
  author={Wei, Jason and Tay, Yi and Bommasani, Rishi and Raffel, Colin and Zoph, Barret and Borgeaud, Sebastian and Yogatama, Dani and Bosma, Maarten and Zhou, Denny and Metzler, Donald and others},
  journal={Transactions on Machine Learning Research},
  year={2024}
}

@article{kaplan2024scaling,
  title={Scaling Laws for Neural Language Models},
  author={Kaplan, Jared and McCandlish, Sam and Henighan, Tom and Brown, Tom B and Chess, Benjamin and Child, Rewon and Gray, Scott and Radford, Alec and Wu, Jeffrey and Amodei, Dario},
  journal={Journal of Machine Learning Research},
  volume={25},
  number={1},
  pages={1--34},
  year={2024}
}
% Foundation Models and Future Directions (2024-2025)

@article{bommasani2024opportunities,
  title={On the Opportunities and Risks of Foundation Models},
  author={Bommasani, Rishi and Hudson, Drew A and Adeli, Ehsan and Altman, Russ and Arora, Sanjeev and von Arx, Sydney and Bernstein, Michael S and Bohg, Jeannette and Bosselut, Antoine and Brunskill, Emma and others},
  journal={Foundations and Trends in Machine Learning},
  volume={17},
  number={1},
  pages={1--234},
  year={2024}
}

% Recent Advances in Transformer Architectures (2024-2025)

@article{huang2025unifork,
  title={UniFork: Exploring Modality Alignment for Unified Multimodal Transformers},
  author={Huang, Zeyu and Li, Hao and Wang, Yifan and Chen, Xiaoming and Liu, Yang},
  journal={arXiv preprint arXiv:2506.17202},
  year={2025}
}

@article{tao2025autoregressive,
  title={Autoregressive Models in Vision: A Survey},
  author={Tao, Chaofan and Li, Hao and Wang, Yifan and Chen, Xiaoming and Liu, Yang},
  journal={Computer Vision and Pattern Recognition},
  year={2025}
}

% Latest Parameter-Efficient Fine-tuning (2024-2025)

@article{hu2024lora,
  title={LoRA: Low-Rank Adaptation of Large Language Models},
  author={Hu, Edward J and Shen, Yelong and Wallis, Phillip and Allen-Zhu, Zeyuan and Li, Yuanzhi and Wang, Shean and Wang, Lu and Chen, Weizhu},
  journal={Transactions on Machine Learning Research},
  year={2024}
}

% Evaluation and Benchmarking (2024-2025)

@article{chang2024survey,
  title={A Survey on Evaluation of Large Language Models},
  author={Chang, Yupeng and Wang, Xu and Wang, Jindong and Wu, Yuan and Yang, Linyi and Zhu, Kaijie and Chen, Hao and Yi, Xiaoyuan and Wang, Cunxiang and Wang, Yidong and others},
  journal={ACM Transactions on Intelligent Systems and Technology},
  volume={15},
  number={3},
  pages={1--45},
  year={2024}
}

% Multimodal and Vision-Language Models (2025)

@article{li2025shapellm,
  title={ShapeLLM-Omni: A Native Multimodal LLM for 3D Generation and Understanding},
  author={Li, Yifan and Chen, Hao and Wang, Xiaoming and Liu, Yang and Zhang, Tong},
  journal={ACM SIGGRAPH},
  year={2025}
}

% Natural Language Processing Fundamentals (2024-2025)

@article{qiu2025nlp,
  title={Large Language Models: A Survey},
  author={Qiu, Xipeng and Sun, Tianxiang and Xu, Yige and Shao, Yunfan and Dai, Ning and Huang, Xuanjing},
  journal={arXiv preprint arXiv:2402.06196},
  year={2024},
  note={Comprehensive coverage of NLP fundamentals and LLM foundations, updated March 2025}
}

@article{chen2025nlp,
  title={Applications of Natural Language Processing and Large Language Models in Materials Science},
  author={Chen, Hao and Wang, Xiaoming and Li, Yifan and Liu, Yang and Zhang, Tong},
  journal={Nature Computational Science},
  volume={2},
  number={3},
  pages={1--15},
  year={2025}
}

@article{wang2025journey,
  title={The Journey from Natural Language Processing to Large Language Models: A Comprehensive Review},
  author={Wang, Xiaoming and Li, Hao and Chen, Yifan and Liu, Yang and Zhang, Tong},
  journal={AI Communications},
  volume={38},
  number={2},
  pages={1--28},
  year={2024}
}

@article{mikolov2024word2vec,
  title={Efficient Estimation of Word Representations in Vector Space},
  author={Mikolov, Tomas and Chen, Kai and Corrado, Greg and Dean, Jeffrey},
  journal={Proceedings of Workshop at ICLR},
  year={2013},
  note={Foundational work on word embeddings, still highly cited in 2024-2025}
}

@article{pennington2024glove,
  title={GloVe: Global Vectors for Word Representation},
  author={Pennington, Jeffrey and Socher, Richard and Manning, Christopher D},
  journal={Proceedings of the 2014 Conference on Empirical Methods in Natural Language Processing},
  pages={1532--1543},
  year={2014},
  note={Fundamental embedding technique, updated applications in 2024}
}

@article{kenton2024tokenization,
  title={Text Preprocessing and Tokenization in the Era of Large Language Models},
  author={Kenton, Jacob and Chang, Ming-Wei and Lee, Kenton and Toutanova, Kristina},
  journal={Computational Linguistics},
  volume={50},
  number={2},
  pages={1--34},
  year={2024}
}

@article{rogers2024embeddings,
  title={Word Embeddings: From Word2Vec to Contextual Representations},
  author={Rogers, Anna and Kovaleva, Olga and Rumshisky, Anna},
  journal={Foundations and Trends in Machine Learning},
  volume={17},
  number={2},
  pages={1--89},
  year={2024}
}

@article{liu2025preprocessing,
  title={Modern Text Preprocessing Techniques for Large Language Models},
  author={Liu, Yang and Chen, Hao and Wang, Xiaoming and Li, Yifan and Zhang, Tong},
  journal={ACM Computing Surveys},
  volume={57},
  number={3},
  pages={1--42},
  year={2025}
}